<template>
  <div class="schedule-list">
    <div class="schedule-list-header">
      <div class="schedule-list-header-left">
        <div style="flex-shrink: 0">{{ $t('198704') }}</div>
        <a-select
          ref="skuSelect"
          :value="selectedSkuIds"
          show-search
          mode="multiple"
          style="width: 300px"
          placeholder="Please select"
          :max-tag-count="1"
          :filter-option="filterSkuOption"
          @change="handleSelectChange"
          @search="handleSearchInput"
        >
          <a-select-option v-for="sku in skuOptions" :key="sku.sku_id" :value="sku.sku_id">
            {{ sku.sku_name }}
          </a-select-option>
          <div slot="dropdownRender" slot-scope="menu">
            <v-nodes :vnodes="menu" />
            <a-divider style="margin: 2px 0" />
            <div
              style="padding: 4px 8px; cursor: pointer"
              @mousedown="(e) => e.preventDefault()"
              @click="toggleSelectAllSku(isAllSkuSelected)"
            >
              <a-icon :type="isAllSkuSelected ? 'minus' : 'plus'" />
              {{ isAllSkuSelected ? 'Unselect All' : 'Select All' }}
            </div>
          </div>
        </a-select>
        <a-button class="search-button" type="primary" @click="applySearch">
          <a-icon type="search" />
        </a-button>
      </div>
      <div class="schedule-list-header-right">
        <div v-for="label in labelList" :key="label.text" class="schedule-list-header-label">
          <svg-icon :icon-name="label.icon" class="schedule-list-header-icon"></svg-icon>
          <span>{{ label.text }}</span>
        </div>
      </div>
    </div>
    <AdvancedFilters
      :units="unitTypes"
      :has-status-filter="true"
      :variants="variants"
      :value="advancedFilters"
      @change="handleAdvancedFiltersChange"
    />
    <div class="schedule-list-wrap">
      <ScheduleCard
        v-for="(sku, index) in filteredSkuList"
        :key="sku.sku_id"
        :sku-data="sku"
        :num="index + 1"
        :data-sku-id="sku.sku_id"
        :opened-id.sync="openedSkuId"
        class="schedule-list-item"
        @update="$emit('refresh-sku')"
        @savePkgStep="handleSavePkgStep"
      />
    </div>
  </div>
</template>

<script>
import { mapState } from 'vuex'
import ScheduleCard from '@aidRevamp/pages/tours/inventory-schedule/schedule/ScheduleCard.vue'
import { formatVariantValueName, filterSkuList } from '@aidRevamp/pages/tours/inventory-schedule/utils'
import { getEditLockValueByPath } from '@aidRevamp/utils/old.js'
import menusMixin from '~src/modules/aidRevamp/mixins/menus.js'
import AdvancedFilters from '@aidRevamp/components/advanced-filters/index.vue'

export default {
  name: 'ScheduleList',
  components: {
    VNodes: {
      functional: true,
      render: (_, ctx) => ctx.props.vnodes
    },
    ScheduleCard,
    AdvancedFilters
  },
  mixins: [menusMixin],
  provide() {
    return {
      skuList: this.skuList,
      skuOptions: this.skuOptions,
      getEditLockValueByPath: (args) => {
        return getEditLockValueByPath({
          data: _.get(this, 'packageFresherSettingData', {}),
          ...args
        })
      }
    }
  },
  props: {
    skuList: {
      type: Array,
      required: true,
      default: () => []
    },
    selectedSkuIds: {
      type: Array,
      required: true,
      default: () => []
    },
    spuId: {
      type: [Number, String],
      default: null
    },
    language: {
      type: String,
      default: 'en_US'
    }
  },
  data() {
    return {
      openedSkuId: null,
      advancedFilters: {
        units: [],
        variants: [],
        statuses: []
      },
      searchText: '',
      currentSearchInput: '' // 存储当前输入框中的文本
    }
  },
  computed: {
    ...mapState({
      skuModel: (state) => state.skuModel,
      unitTypes: (state) => state.unitTypes,
      variants: (state) => state.variants
    }),
    labelList() {
      const { merchant_currency, selling_currency } = this.skuModel || {}
      return [
        {
          text: this.$t('81821'),
          icon: 'icon_inventory'
        },
        {
          text: `${this.$t('81823')}(${merchant_currency})`,
          icon: 'finance'
        },
        {
          text: `${this.$t('81824')}(${selling_currency})`,
          icon: 'Icon_common_currency_line'
        }
      ]
    },
    skuOptions() {
      return this.skuList.map((sku) => {
        const { sku_id, variant_list, unit_type_name } = sku
        return {
          sku_id,
          sku_name: formatVariantValueName(variant_list, unit_type_name)
        }
      })
    },
    skuOptionsIds() {
      return this.skuOptions.map((sku) => sku.sku_id)
    },
    filteredSkuList() {
      // 使用更新后的filterSkuList函数，直接传入searchText
      return filterSkuList(this.skuList, this.selectedSkuIds, this.advancedFilters, this.searchText)
    },
    isAllSkuSelected() {
      return this.selectedSkuIds.length === this.skuOptionsIds.length
    }
  },
  created() {
    klook.bus.$off('openAndScrollSku').$on('openAndScrollSku', this.openAndScrollSku)
  },
  mounted() {
    this.initActiveIdFromUrl()
    document.addEventListener('keydown', this.handleKeyDown)
  },
  beforeDestroy() {
    klook.bus.$off('openAndScrollSku', this.openAndScrollSku)
    document.removeEventListener('keydown', this.handleKeyDown)
    // 清理搜索定时器
    if (this.searchTimer) {
      clearTimeout(this.searchTimer)
    }
  },

  methods: {
    initActiveIdFromUrl() {
      const { sku_id } = this.$route.query
      const skuId = Number(sku_id)
      // 验证sku_id的合法性
      if (this.skuOptionsIds.includes(skuId)) {
        this.openAndScrollSku(skuId)
      }
    },
    filterSkuOption(input, option) {
      if (String(option.componentOptions?.propsData?.value || '').startsWith(input)) {
        return true
      }
      return option.componentOptions.children[0].text.toLowerCase().indexOf(input.toLowerCase()) >= 0
    },
    handleSelectChange(val) {
      this.$emit(
        'update:selectedSkuIds',
        this.skuOptionsIds.filter((id) => val.includes(id))
      )
    },
    toggleSelectAllSku(isSelectedAll) {
      this.$emit('update:selectedSkuIds', isSelectedAll ? [] : [...this.skuOptionsIds])
    },
    handleAdvancedFiltersChange(filters) {
      this.advancedFilters = filters
      this.$emit('advanced-filters-change', filters)
    },

    // 处理搜索输入
    handleSearchInput(value) {
      this.currentSearchInput = value
    },
    // 应用搜索
    applySearch() {
      // 将当前输入框的文本设置为搜索文本，触发 filteredSkuList 重新计算
      this.searchText = this.currentSearchInput
    },
    handleKeyDown(event) {
      // 检查焦点是否在搜索框内
      const selectElement = this.$refs.skuSelect?.$el
      const isSelectFocused = selectElement && selectElement.contains(document.activeElement)

      if (event.key === 'Enter' && isSelectFocused) {
        event.preventDefault() // 防止默认的 Enter 行为
        this.applySearch()
      }
    },
    async handleSavePkgStep() {
      await this.onUpdateMenuStatus('ALL')
      // TODO: 是否要刷新 package 状态？
      // if (this.isCreate) {
      // klook.bus.$emit('updatePkgInfos2bus', this.package_id)
      // }
    },
    scrollSkuToView(id) {
      const skuDom = document.querySelector(`.schedule-card[data-sku-id="${id}"]`)
      if (skuDom && window) {
        var headerOffset = 64
        var elementPosition = skuDom.getBoundingClientRect().top
        var offsetPosition = elementPosition + window.scrollY - headerOffset

        window.scrollTo({
          top: offsetPosition,
          behavior: 'smooth'
        })
      }
    },
    openAndScrollSku(skuId) {
      this.openedSkuId = skuId
      this.$nextTick(() => this.scrollSkuToView(skuId))
    }
  }
}
</script>
<style lang="scss" scoped>
.schedule-list {
  &-header {
    padding: 16px;
    display: flex;
    align-items: center;
    justify-content: flex-start;
    flex-wrap: wrap;
    gap: 16px;
    background-color: #e6f1ff;
    border-radius: 4px 4px 0 0;

    &-left,
    &-right {
      display: flex;
      align-items: center;
      justify-content: flex-start;
      gap: 4px;
      color: rgb(0, 0, 0, 0.8);
    }

    &-left {
      flex-shrink: 0;
    }

    &-right {
      flex-wrap: wrap;
      gap: 16px;
    }

    &-icon {
      margin-right: 4px;
      flex-shrink: 0;
    }

    &-label {
      flex-shrink: 0;
    }
  }

  &-wrap {
    padding: 16px;
    background-color: #fafafa;
  }

  &-item {
    margin-top: 16px;

    &:first-child {
      margin-top: 0;
    }
  }
}

.search-wrapper {
  display: flex;
  align-items: center;
}

.search-button {
  margin-left: 8px;
  height: 32px;
  padding: 0 8px;
}
</style>

<template>
  <a-modal
    class="sync-api-setting-container"
    :visible.sync="_visible"
    :width="920"
    :title="$t('73518')"
    @cancel="handleClose"
  >
    <header class="header">
      {{ $t('73519') }}
    </header>

    <div class="body">
      <div class="date-range">
        <span class="__label">{{ $t('73521') }}</span>
        <a-range-picker v-model="dateRange" :disabled="!checkedList.length" :disabled-date="disabledDate" />
      </div>
      <div class="operator">
        <p class="__label">Select sku to sync for package date range</p>
        <!-- 高级筛选组件 -->
        <div class="__advanced-filters">
          <AdvancedFilters
            :has-status-filter="true"
            :units="unitTypes"
            :variants="variants"
            :spu-id="packageId"
            :value="advancedFilters"
            @change="handleAdvancedFiltersChange"
          />
        </div>
        <div class="__checkbox">
          <a-checkbox :indeterminate="indeterminate" :checked="checkAll" @change="onCheckAllChange">
            Check all
          </a-checkbox>

          <span class="__selected">Selected {{ checkedList.length }} items</span>
        </div>
      </div>

      <div class="content">
        <a-checkbox-group v-model="checkedList" class="unit-list" @change="onChange">
          <div
            v-for="unit in unitList"
            :key="unit.sku_id"
            class="unit-item"
            :class="{
              'is-checked': !checkedList.includes(unit.sku_id)
            }"
          >
            <div class="unit-item-content">
              <a-checkbox :value="unit.sku_id" :disabled="!supportedSkuIds.includes(unit.sku_id)" />
              <div class="unit-info">
                <div class="__name">
                  {{ getUnitName(unit) }}
                </div>
                <div class="__tags">
                  <a-tag>ID: {{ unit.sku_id || '-' }}</a-tag>
                  <a-tag :color="unit.published ? '#87d068' : '#f0f0f0'">
                    Status:
                    {{
                      unit.published ? $t('package_list_unit_published') : $t('package_list_unit_unpublished')
                    }}
                  </a-tag>
                  <a-tag v-if="getRelationshipText(unit) !== '-'" color="#ffc53d">
                    {{ getRelationshipText(unit) }}
                    <a-tooltip v-if="getRelationshipTip(unit)" placement="top" arrow-point-at-center>
                      <template slot="title">
                        <span>{{ getRelationshipTip(unit) }}</span>
                      </template>
                      <a-icon type="info-circle" theme="filled" style="margin-left: 4px; color: #ff9c00" />
                    </a-tooltip>
                  </a-tag>
                </div>
              </div>
            </div>

            <div v-if="!supportedSkuIds.includes(unit.sku_id)" class="__no-sync-warn">{{ $t('73522') }}</div>
          </div>
        </a-checkbox-group>
      </div>
    </div>

    <template slot="footer">
      <a-button @click="handleClose">
        {{ $t('global_cancel') }}
      </a-button>
      <a-button type="primary" :disabled="!canBeSubmit" @click="handleConfirm">
        {{ $t('global_submit') }}
      </a-button>
    </template>
  </a-modal>
</template>

<script>
import { mapState } from 'vuex'
import { bestMatchLang, pmsConfirm } from '@aidRevamp/utils/old.js'
import {
  isSPUType,
  isSPUTypeDefault,
  isHotelType,
  isNotSPUType,
  SHARE_RELATIONSHIPS
} from '@aidRevamp/components/calendar-setting/package_const.js'
import AdvancedFilters from '@aidRevamp/components/advanced-filters/index.vue'
import moment from 'moment'
import { formatVariantValueName } from '@aidRevamp/pages/tours/inventory-schedule/utils'

export default {
  name: 'ApiSyncSetting',
  components: {
    AdvancedFilters
  },
  inject: ['packageId'],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    unitList: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      checkedList: [],
      checkAll: true,
      indeterminate: false,
      dateRange: [],
      advancedFilters: {
        units: [],
        variants: []
      }
    }
  },
  watch: {
    visible: {
      immediate: true,
      handler(v) {
        if (v) {
          Object.assign(this, {
            checkedList: _.cloneDeep(this.supportedSkuIds),
            checkAll: true,
            indeterminate: false,
            dateRange: [moment(), moment().add(3, 'months')]
          })
        }
      }
    }
  },
  computed: {
    ...mapState({
      unitTypes: (state) => state.unitTypes,
      variants: (state) => state.variants
    }),
    ...mapState({
      packageFresherSettingData: 'packageFresherSettingData'
    }),
    _visible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    supportedSkuIds() {
      return (
        _.get(
          this.packageFresherSettingData,
          'package.page_setting.api_sync_button.data.supported_sku_ids',
          []
        ) || []
      )
    },
    canBeSubmit() {
      return this.dateRange?.length === 2 && this.checkedList.length
    }
  },
  methods: {
    getUnitName(unit) {
      const { variant_list, unit_type_name } = unit
      return formatVariantValueName(variant_list, unit_type_name)
    },
    disabledDate(current) {
      return (
        current &&
        ((this.dateRange[0] && current > moment().add(6, 'months').startOf('day')) ||
          current < moment().startOf('day') ||
          current > moment().add(10, 'years').startOf('day'))
      )
    },
    getRelationshipText(data) {
      const { step, share_rel_ship } = data
      // 如果step没数据，就显示-
      if (!step) {
        return '-'
      }

      const target = SHARE_RELATIONSHIPS.find((item) => item.value === share_rel_ship)

      return target ? target.text : '-'
    },
    getRelationshipTip(data) {
      const { share_rel_ship } = data

      if (share_rel_ship === 1) {
        return this.$t('73522')
      }

      return ''
    },
    getUnitTypeName(data) {
      const { unitType, unit_type, local } = data || {}

      const unitTypeList = [...isSPUType, ...isSPUTypeDefault, ...isHotelType, ...isNotSPUType]
      const target = unitTypeList.find((item) => item.value === (unitType == null ? unit_type : unitType))

      if (target) {
        return target.text
      }

      // 已保存的
      return bestMatchLang('unit_name', 'language', local || []) || '-'
    },
    handleClose() {
      this._visible = false
    },
    async handleConfirm() {
      let temp = await pmsConfirm.call(this, {
        content: this.$t('73523')
      })

      if (!temp) {
        return
      }

      let [start_date, end_date] = this.dateRange

      start_date = moment(start_date).format('YYYY-MM-DD')
      end_date = moment(end_date).format('YYYY-MM-DD')

      await ajax.postBody(ADMIN_API.act.sync_skus_inv_price, {
        data: {
          package_id: +this.packageId,
          start_date,
          end_date,
          skus: this.checkedList.map((sku_id) => ({
            sku_id
          }))
        }
      })

      this.$message.success(this.$t('global_success'))
      this.$emit('updated')
      klook.bus.$emit('refreshUnitList', { clearActiveId: false })
      this.handleClose()
    },
    onChange(checkedList) {
      this.indeterminate = !!checkedList.length && checkedList.length < this.supportedSkuIds.length
      this.checkAll = checkedList.length === this.supportedSkuIds.length
    },
    onCheckAllChange(e) {
      Object.assign(this, {
        checkedList: e.target.checked ? _.cloneDeep(this.supportedSkuIds) : [],
        indeterminate: false,
        checkAll: e.target.checked
      })
    },
    // 处理高级筛选变化
    handleAdvancedFiltersChange(filters) {
      // this.advancedFilters = filters
      // // 当筛选条件变化时，重新计算选中状态
      // const filteredSupportedIds = this.filteredUnitList
      //   .map((unit) => unit.sku_id)
      //   .filter((id) => this.supportedSkuIds.includes(id))
      // // 更新选中列表，只保留在筛选结果中的项目
      // this.checkedList = this.checkedList.filter((id) => filteredSupportedIds.includes(id))
      // // 更新全选状态
      // this.checkAll =
      //   this.checkedList.length === filteredSupportedIds.length && filteredSupportedIds.length > 0
      // this.indeterminate =
      //   this.checkedList.length > 0 && this.checkedList.length < filteredSupportedIds.length
    }
  }
}
</script>

<style scoped lang="scss">
.sync-api-setting-container {
  ::v-deep {
    .ant-modal-header {
      border-bottom: 1px solid #f0f0f0;
    }
    .ant-modal-footer {
      padding: 10px 16px;
      border-top: 1px solid #f0f0f0;
    }
  }

  .header {
    padding: 10px 16px;
    background-color: #fffbe6;
    border: 1px solid #ffe58f;
    border-radius: 2px;
  }

  .body {
    margin: 20px 0;

    .date-range {
      margin-bottom: 20px;

      .__label {
        display: block;
        margin-bottom: 2px;
        font-weight: 400;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
      }
    }

    .operator {
      margin-bottom: 10px;

      .__label {
        font-weight: 500;
        color: #212121;
      }

      .__checkbox {
        margin-top: 12px;
        padding: 0 12px;
        display: flex;
        align-items: center;
        justify-content: flex-start;
        flex-wrap: wrap;
        gap: 12px;

        .__selected {
          margin-left: 16px;
          color: #757575;
        }

        .__advanced-filters {
          margin-left: auto;
        }
      }
    }

    .unit-list {
      width: 100%;
    }

    .unit-item {
      padding: 16px 12px;
      margin-bottom: 10px;
      border: 1px solid #e6e6e6;
      border-radius: 4px;

      &.is-checked {
        border-color: #2073f9;
        background-color: #fff;
      }

      &-content {
        display: flex;
        align-items: flex-start;
        gap: 12px;
      }

      .unit-info {
        flex: 1;
        display: flex;
        flex-direction: column;
        gap: 8px;

        .__name {
          font-weight: 500;
          color: rgba(0, 0, 0, 0.85);
          line-height: 1.5;
        }

        .__tags {
          display: flex;
          flex-wrap: wrap;
          gap: 8px;
        }
      }

      .__no-sync-warn {
        margin-top: 16px;
        padding: 10px 16px;
        background-color: #fff1f0;
        border: 1px solid #ffccc7;
        border-radius: 2px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.85);
      }
    }
  }
}
</style>

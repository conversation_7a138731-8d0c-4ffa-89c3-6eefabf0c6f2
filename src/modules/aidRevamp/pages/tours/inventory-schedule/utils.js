import { variantUseStatus, variantEditStatus } from '@aidRevamp/pages/tours/inventory-schedule/consts'

export function formatVariantValueName(variant = [], unitName = '') {
  const copy = [...(variant || [])]
  copy.sort((a, b) => a.variant_priority - b.variant_priority)
  const list = copy.map((item) => item.variant_value_name)
  if (unitName) {
    list.push(unitName)
  }
  return list.join(' · ')
}

export function formatVariantId(variantList) {
  const list = (variantList || []).map((item) => `${item.variant_id}:${item.variant_value_id}`)
  list.sort()
  return list.join('-')
}

export function isStatusSelected({ use_status }) {
  return use_status === variantUseStatus['selected']
}

export function isVariantInvalid({ variant_value_list }) {
  return !(variant_value_list || []).some(isStatusSelected)
}

export function isEditForbidden({ edit_permission }) {
  return edit_permission === variantEditStatus['forbidden']
}

export function isEditCanDelete({ edit_permission }) {
  return edit_permission === variantEditStatus['delete']
}

/**
 * 根据搜索文本过滤SKU列表
 * @param {Array} skuList - SKU列表
 * @param {string} searchText - 搜索文本
 * @returns {Array} - 过滤后的SKU列表
 */
export function searchSkuList(skuList = [], searchText = '') {
  if (!searchText || !searchText.trim()) {
    return skuList
  }

  const searchLower = searchText.toLowerCase().trim()

  return skuList.filter((sku) => {
    // 搜索SKU ID
    if (String(sku.sku_id).includes(searchLower)) {
      return true
    }

    // 搜索Unit名称
    if ((sku.unit_type_name || '').toLowerCase().includes(searchLower)) {
      return true
    }

    // 搜索Variant值名称
    const hasMatchingVariant = (sku.variant_list || []).some((variant) =>
      variant.variant_value_name.toLowerCase().includes(searchLower)
    )
    if (hasMatchingVariant) {
      return true
    }

    // // 搜索Variant名称
    // const hasMatchingVariantName = (sku.variant_list || []).some((variant) =>
    //   variant.variant_name.toLowerCase().includes(searchLower)
    // )
    // if (hasMatchingVariantName) {
    //   return true
    // }

    // // 搜索格式化后的完整SKU名称
    // const fullSkuName = formatVariantValueName(sku.variant_list, sku.unit_type_name).toLowerCase()
    // if (fullSkuName.includes(searchLower)) {
    //   return true
    // }

    return false
  })
}

// 根据所选 skuIds 以及 unit / variant 高级筛选条件，返回过滤后的 sku 列表
export function filterSkuList(
  skuList = [],
  selectedSkuIds = [],
  advancedFilters = { units: [], variants: [], statuses: [] },
  searchText = ''
) {
  // 预先检查过滤条件是否存在，避免不必要的循环
  const hasSkuFilter = Array.isArray(selectedSkuIds) && selectedSkuIds.length > 0
  const hasUnitFilter = Array.isArray(advancedFilters.units) && advancedFilters.units.length > 0
  const hasVariantFilter = Array.isArray(advancedFilters.variants) && advancedFilters.variants.length > 0
  const hasStatusFilter = Array.isArray(advancedFilters.statuses) && advancedFilters.statuses.length > 0
  const hasSearchFilter = !!searchText && searchText.trim() !== ''

  // 如果没有任何过滤条件，直接返回原数组
  if (!hasSkuFilter && !hasUnitFilter && !hasVariantFilter && !hasStatusFilter && !hasSearchFilter) {
    return skuList
  }

  // 一次遍历完成所有过滤
  let filteredList = skuList.filter((sku) => {
    // SKU ID 过滤
    if (hasSkuFilter && !selectedSkuIds.includes(sku.sku_id)) {
      return false
    }

    // Unit 过滤
    if (hasUnitFilter && !advancedFilters.units.includes(sku.unit_type_id)) {
      return false
    }

    // Variant 过滤
    if (hasVariantFilter) {
      // 预先计算 variant_value_id 集合，避免每次都映射
      const skuVariantIds = new Set((sku.variant_list || []).map((v) => v.variant_value_id))
      if (!advancedFilters.variants.some((id) => skuVariantIds.has(id))) {
        return false
      }
    }

    // 发布状态过滤
    if (hasStatusFilter) {
      const isPublished = !!sku.published
      if (!advancedFilters.statuses.includes(isPublished)) {
        return false
      }
    }

    return true
  })

  // 搜索文本过滤
  if (hasSearchFilter) {
    filteredList = searchSkuList(filteredList, searchText)
  }

  return filteredList
}

<template>
  <div class="common-layout-wrapper" :data-spm-page="calcPageSpm">
    <div v-show="globalLoading && globalLoading.switch" class="app-spin-wrapper">
      <div class="app-spin-blur" :style="{ opacity: globalLoading && globalLoading.switch ? 0.5 : 0 }" />
      <a-spin :tip="globalLoading && globalLoading.tip">
        <a-icon slot="indicator" type="loading" style="font-size: 50px" spin />
      </a-spin>
    </div>

    <div class="layout">
      <div v-if="hasHeaderContent" class="layout-header" :style="{}">
        <slot name="header">
          <Header :data-spm-page="calcPageSpm + '&trg=manual'" @syncDataUpdate="syncDataUpdateHandler" />
        </slot>
      </div>
      <div class="layout-tips">
        <a-alert v-if="migrationActAlertMsg" type="warning">
          <span slot="message">{{ $t('76490') }}</span>
          <span slot="description" v-html="$t('213324')"></span>
        </a-alert>
      </div>
      <div ref="layoutMain" v-disabled="calcLayoutPageDisabled" class="layout-main">
        <template v-if="hasSidebarContent">
          <a-affix :offset-top="styleData.sysHeight" @change="changeAffixedHandler">
            <div class="layout-sidebar" :style="calcMenusStyle">
              <div :style="{ padding: `0 0 ${styleData.headerHeight}px 0` }">
                <slot name="sidebar">
                  <CommonMenus
                    ref="commonMenusRef"
                    v-bind="{
                      ...menusData,
                      stepStatusData: spuStepStatusDataGetter,
                      list: menuList
                    }"
                    @change="menuChange"
                  />
                </slot>
              </div>
            </div>
          </a-affix>
        </template>
        <div class="layout-right">
          <div class="layout-content">
            <a-spin v-if="refreshKey" :spinning="loading">
              <keep-alive>
                <router-view v-if="$route.meta.keepAlive" ref="childComponent" />
              </keep-alive>
              <router-view v-if="!$route.meta.keepAlive" ref="childComponent" />
            </a-spin>
          </div>
        </div>
        <div v-if="hasFooterContent" class="layout-footer" :style="footerStyle">
          <slot name="footer">
            <Footer
              :buttons="getButtons()"
              :pageType="pageType"
              :pageId="pageId"
              :pagePath="currentPagePath"
            />
          </slot>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {
  PAGE_TYPE_DICT,
  TOURS_MENU_KEY_DICT,
  ATTRACTION_MENU_KEY_DICT,
  PAGE_LEVEL_DICT
} from '~src/modules/aidRevamp/utils/const.js'
import throttle from 'lodash/throttle'
import CommonMenus from '~src/modules/aidRevamp/components/common/menus/index.vue'
import Footer from '~src/modules/aidRevamp/components/common/layout/footer.vue'
import Header from '~src/modules/aidRevamp/components/common/layout/header.vue'
import { mapState, mapGetters, mapActions, mapMutations } from 'vuex'
import { saveProduct } from '~src/modules/aidRevamp/api/commom'
import {
  SET_LOADING,
  SET_GLOBAL_ROUTE_QUERY,
  SET_GLOBAL_LOADING
} from '~src/modules/aidRevamp/store/modules/global/mutation-types'
import { SET_CURR_MENU_ITEM_DATA } from '~src/modules/aidRevamp/store/modules/common/mutation-types.js'
import { scrollFormItemExplain } from '~src/modules/aidRevamp/utils/index.js'
import { fmtSaveDataIncludeDisplay } from '~src/modules/aidRevamp/components/common/obt-section/utils/index.js'
import AidDirective from '~src/modules/aidRevamp/directive/index.js'
import { isAttraction } from '~src/modules/aidRevamp/utils/index.js'
import EditProtect from '~src/modules/aidRevamp/components/common/layout/editProtect.js'
import { PAGE_PATH_DICT, PAGE_PATH_ENUM } from '~src/modules/aidRevamp/components/common/feedback'
import { isMerchant } from '@/env'

AidDirective.install()

const getActCategory2action = 'getActCategory2action'

export default {
  name: 'ManualLayout',
  components: {
    CommonMenus,
    Footer,
    Header
  },
  mixins: [EditProtect],
  provide() {
    return {
      refreshPage: this.refreshPage,
      rootChangeLoading: this.rootChangeLoading,
      goForward: this.goForward,
      findNextMenuItem: this.findNextMenuItem,
      saveStandardPage: this.saveStandardPage
    }
  },
  props: {
    sidebarWidth: {
      type: Number,
      default: 230
    },
    pageLevel: {
      type: String,
      default: PAGE_LEVEL_DICT.att,
      required: true
    },
    pagePathType: {
      type: String,
      default: PAGE_PATH_ENUM.ATTRACTION,
      required: true
    },
    pageName: {
      type: String,
      default: () => '',
      required: false
    },
    menusData: {
      type: Object,
      default: () => ({
        list: [],
        menusNameMap: {}
      }),
      required: true
    },
    footerButtons: {
      type: Array,
      required: false,
      default: () => []
    },
    hideHeader: {
      type: Boolean,
      default: false
    },
    hideSidebar: {
      type: Boolean,
      default: false
    },
    hideFooter: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isMerchant,
      styleData: {
        sysHeight: 64, // 脚手架默认高度
        headerHeight: 0, // layout header的高度
        headerIsAffixed: false
      },
      loading: false,
      refreshKey: true,
      // 当前菜单项
      saveCallback: null,
      isSaving: false,
      isFixed: false,
      fixedTop: 0,
      scrollThreshold: 100,
      mainOffsetLeft: 0,
      // 菜单项
      menuItems: [],
      // 菜单项，包含了菜单项是否完成
      menuItemsWithFinished: [],
      preSaveHooks: [],
      postSaveHooks: [],
      firstSpuId: 0
    }
  },
  computed: {
    ...mapState(['lockMerchantActEditing']),
    ...mapGetters(['lockMerchantEditFreeText2getter']),
    ...mapGetters('common', ['menuList', 'spuStepStatusDataGetter']),
    ...mapGetters('global', ['isLocking', 'globalSaveHidden']),
    ...mapGetters('global', ['globalSaveIsLoading', 'globalSaveDisabled', 'globalLoading']),
    ...mapGetters({
      showMigrationActivityHintGetter: 'showMigrationActivityHintGetter'
    }),
    migrationActAlertMsg() {
      if (this.showMigrationActivityHintGetter) {
        return this.$t('213324')
      }

      return ''
    },
    footerStyle() {
      return {
        left: this.sidebarWidth + (isMerchant ? 0 : 56) + 'px'
      }
    },
    currentMenuKey() {
      return this.currentItemData?.menu_key
    },
    currentItemData() {
      const currentName = this.$route.name
      const map = this.menusData?.menusNameMap || {}
      const key = Object.keys(map || {}).find((key) => map[key] === currentName)
      const currentMenu = this.findMenuItem(key)
      return currentMenu
    },
    currentPageUrl() {
      return this.$route.path
    },
    currentPagePath() {
      return this.currentItemData?.menu_name || ''
    },
    currentItemRequired() {
      return this.currentItemData?.extra_info?.required
    },
    calcMenusStyle() {
      const { sysHeight } = this.styleData
      const menuHeight = `calc(100vh - ${sysHeight}px)`
      const obj = {
        width: this.sidebarWidth + 'px',
        height: menuHeight,
        overflow: 'hidden',
        overflowY: 'auto'
      }
      return obj
    },
    calcLayoutPageDisabled() {
      const deleteElements = [
        // departure
        '.departure-return-page .card-index-action .delete-box',
        '.departure-return__group-box .list-box__item-box .delete-icon',
        //
        '.location-item-actions .location-item-action',
        // itinerary
        '.itinerary-page .card-index-action .delete-box',
        '.itinerary-comp .js-btns-box',
        // photo
        '.photo-edit-image i.handle-icon.anticon-upload',
        '.photo-edit-image i.handle-icon.anticon-edit',
        '.photo-gallery-btns',
        '.photo-edit-body .photo-operate',
        // policy + detail-info
        '.group-item-container .compound-option__operation',
        '.group-item-container .anticon-delete',
        // included
        '.aid-include-group-item .choose-tag-add-new',
        '.aid-include-group-item .choose-tag-item .anticon-close',
        // detail-info
        '.presenter-container .footer-go-ahead',
        // other info
        '.customOtherInfo-container .footer',
        // unit-type
        '.unit-type-page .people-box__name-box .js-drag-flag',
        // Attraction grouping
        '.grouping-operate-area .operation-block',
        '.grouping-operate-area .add-item-box'
      ]

      if (this.isLocking) {
        return {
          lock: true,
          deleteElements
        }
      }

      if (isMerchant) {
        if (this.lockMerchantActEditing) {
          return {
            lock: true,
            deleteElements
          }
        }

        return {
          lock: this.lockMerchantEditFreeText2getter,
          defaultScope: 'freeText',
          deleteElements
        }
      }

      return {
        lock: this.$root.superEditLock,
        extendSelectorList: ['.variant-checkbox'],
        deleteElements
      }
    },
    calcPageSpm() {
      const { spmPageName } = this.$route.meta
      const oid = `activity_${this.aid || 0}`
      const ext = {
        IsSPU: Number(this.calcIsSpuType)
      }
      const str = `${spmPageName}?oid=${oid}&ext=${JSON.stringify(ext)}`
      return str
    },
    pkgId() {
      return +this.$route.query.package_id
    },
    aid() {
      return Number(this.$route.params.id)
    },
    pageFrom() {
      return this.$route.query.page_from || 'admin'
    },
    editStatus() {
      return this.$route.query.edit_status || 0
    },
    language() {
      return this.$route.query.lang || 'en_US'
    },
    calcIsSpuType() {
      return this.pageLevel === PAGE_LEVEL_DICT.spu
    },
    spuId() {
      return this.$route.query?.spu_id || this.$route.query?.package_id || ''
    },
    referLanguage() {
      return this.$route.query.ref
    },
    hasHeaderContent() {
      return !(this.hiddenHeader || this.$route.meta?.hiddenHeader)
    },
    hasSidebarContent() {
      return !(this.hiddenSidebar || this.$route.meta?.hiddenSidebar)
    },
    hasFooterContent() {
      return !(this.hiddenFooter || this.$route.meta?.hiddenFooter)
    },
    policyKeys() {
      return [TOURS_MENU_KEY_DICT.policy.key, ATTRACTION_MENU_KEY_DICT.policy.key]
    },
    isFirstItem() {
      return !this.findPreviousMenuItem()
    },
    isLastItem() {
      return !this.findNextMenuItem()
    },
    isAttr() {
      return isAttraction(this.subcategoryId)
    },
    isTours() {
      return this.pageLevel === PAGE_LEVEL_DICT.spu && !this.isAttr
    },
    pageType() {
      return PAGE_PATH_DICT[this.pagePathType]?.type || ''
    },
    pageId() {
      const routeMap = {
        [PAGE_PATH_ENUM.ATTRACTION]: this.aid,
        [PAGE_PATH_ENUM.ATTRACTION_SPU]: this.spuId,
        [PAGE_PATH_ENUM.TOURS_SPU]: this.$route.params.id
      }
      return routeMap[this.pagePathType] || 0
    }
  },
  watch: {
    $route: {
      handler(newRoute, oldRoute) {
        // 只在必要时触发 fetchMenuData
        if (
          newRoute.query.package_id !== oldRoute?.query.package_id ||
          newRoute.query.lang !== oldRoute?.query.lang
        ) {
          this.fetchMenuData()
        }

        window.scrollTo({
          top: 0,
          behavior: 'smooth'
        })
      }
    },
    currentItemData: {
      deep: true,
      immediate: true,
      handler(data) {
        this[SET_CURR_MENU_ITEM_DATA](data || {})
        this.getSectionDataByMenu(data || {})
      }
    },
    pageFrom: {
      immediate: true,
      handler(page_from) {
        const isBdAudit = ['bd_audit'].includes(page_from)
        this.$store.commit('setMerchantPage', this.$root.isMerchant)
        this.$store.commit('setBdAuditPage', this.$root.isAdmin && isBdAudit)
        this.$store.state.isMC2BD = this.$root.isMerchant || isBdAudit
      }
    },
    calcLayoutPageDisabled: {
      deep: true,
      handler(newV, oldV) {
        if (oldV?.lock && !newV?.lock) {
          window.location.reload()
        }
      }
    }
  },
  created() {
    klook.bus.$off('addHandleSave2bus').$on('addHandleSave2bus', this.registerSaveCallback)
    klook.bus.$off('removeHandleSave2bus').$on('removeHandleSave2bus', this.removeSaveHandler)
    klook.bus.$off('fetchMenuData2bus').$on('fetchMenuData2bus', this.fetchMenuData)
    this.fetchMenuData()
    this[SET_GLOBAL_ROUTE_QUERY](this.$route.query)
  },
  async mounted() {
    this.handleScroll = throttle(this.onScroll, 100)
    this.initMainData()
    this.initLayout()
    window.addEventListener('scroll', this.handleScroll)
    // window.addEventListener('resize', this.initLayout)
    document.body.setAttribute('data-spm-page', `${this.calcPageSpm}&trg=manual`)

    if (isMerchant) {
      this.$store.dispatch('common/initGuidanceVideo', {
        language: lang_conf.getLangObj('F_LANG', 'B_LANG')[KLK_LANG]
      })
    }
  },
  destroyed() {
    klook.bus.$off('addHandleSave2bus', this.registerSaveCallback)
    klook.bus.$off('removeHandleSave2bus', this.removeSaveHandler)
  },
  methods: {
    ...mapMutations('global', [SET_GLOBAL_ROUTE_QUERY, SET_LOADING, SET_GLOBAL_LOADING]),
    ...mapMutations('common', [SET_CURR_MENU_ITEM_DATA]),
    ...mapActions([
      getActCategory2action,
      'common/getMenuList2action',
      'section/getSectionDataByMenu',
      'common/getSpuStepStatus2action',
      'common/updateMenuStatus2action',
      'common/getAttrSpuStepStatus2action',
      'common/updateAttrMenuStatus2action'
    ]),
    changeAffixedHandler(isb) {
      this.styleData.headerIsAffixed = isb
    },
    syncDataUpdateHandler(data) {
      this.styleData.headerHeight = data.height || 0
    },
    getButtons() {
      const buttons = [
        {
          key: 'prev_step',
          className: 'v-disabled-exclude',
          label: this.$t('198538'),
          action: this.goBack,
          props: {
            type: 'default',
            disabled: this.isFirstItem
          }
        },
        {
          key: 'save_and_next_step',
          label: this.isLastItem ? this.$t('global_save') : this.$t('save_and_next'),
          action: this.saveThisSection,
          props: {
            ...{
              'data-spm-module': 'PageSave?trg=manual',
              'data-spm-virtual-item': '__virtual?trg=manual'
            },
            type: 'primary',
            loading: this.globalSaveIsLoading,
            disabled: this.globalSaveDisabled || this.$root.superEditLock || this.isLocking
          }
        }
      ]
      if (!this.currentItemRequired && !this.isLastItem) {
        buttons.splice(1, 0, {
          key: 'next_step',
          className: 'v-disabled-exclude',
          label: this.$t('financial_model_skip'),
          action: this.goForward,
          props: {
            type: 'default'
          }
        })
      }

      if (this.globalSaveHidden) {
        return buttons.filter((button) => button.key !== 'save_and_next_step')
      }

      return buttons
    },
    removeSaveHandler({ preSaveHook, postSaveHooks }) {
      if (preSaveHook) {
        this.preSaveHooks = (this.preSaveHooks || []).filter((hook) => hook !== preSaveHook)
      }
      if (postSaveHooks) {
        this.postSaveHooks = (this.postSaveHooks || []).filter((hook) => hook !== postSaveHooks)
      }
    },
    async fetchMenuData() {
      const pageFrom = this.$route.query.page_from || 'admin'
      const data = await this.getActCategory2action({
        activity_id: this.aid,
        page_from: pageFrom
      })
      if (data?.sub_category_id) {
        await this['common/getMenuList2action']({
          spu_id: this.spuId,
          sub_category_id: data.sub_category_id,
          level: this.pageLevel,
          refresh: true,
          page_from: pageFrom
        })

        this.firstSpuId = data.spu_id_list?.[0]
        this.fetchMenuStep().then(() => {
          this.initCheckNavAuth(this.currentItemData)
        })
        this.subcategoryId = data.sub_category_id
      }
      // this.menuChange(this.currentItemData, true)
    },
    getInitList(arr) {
      this.menuItemsWithFinished = arr
    },
    initCheckNavAuth(obj) {
      const { spuStepStatusDataGetter: spuStatus } = this
      const { menu_key, extra_info } = obj || {}
      const isb = spuStatus?.[menu_key] || extra_info?.depend_on_menus?.every((k) => spuStatus[k])
      if (!isb) {
        let name = ''
        if (this.pageLevel === PAGE_LEVEL_DICT.att) {
          name = ATTRACTION_MENU_KEY_DICT.nameAndLocation.page
        } else {
          if (this.isAttr) {
            name = ATTRACTION_MENU_KEY_DICT.spuBasicInfo.page
          } else {
            name = TOURS_MENU_KEY_DICT.basic.page
          }
        }
        // 当前步骤未完成，或者依赖步骤未完成，则跳转到basic路由
        const { params, query } = this.$route
        const route = this.$router.resolve({
          name,
          params,
          query
        })
        window.open(route.href, '_self')
      }
      return isb
    },
    async getSectionDataByMenu(menu) {
      // 处理标准页面数据获取
      const isStandardPage = menu?.extra_info?.page_type === PAGE_TYPE_DICT.standard
      // policy 页面自行处理了楼层数据和结构
      const isPolicyPage = this.policyKeys.includes(menu?.menu_key)
      if (isStandardPage && !isPolicyPage) {
        const payload = {
          spu_id: this.pkgId,
          sub_category_id: menu.subcategory_id,
          menu_key: menu.menu_key,
          page_level: this.pageLevel,
          page_from: this.pageFrom,
          language: this.language,
          activity_id: this.aid
        }
        if (this.referLanguage) {
          payload.refer_language = this.referLanguage
        }
        await this['section/getSectionDataByMenu'](payload)
      }
    },
    async menuChange(menu, init = false) {
      if (!menu?.menu_key) {
        console.warn('Invalid menu data received')
        return
      }
      // 避免重复调用 - 检查当前菜单键和初始化状态
      if (this.currentMenuKey === menu.menu_key && !init && !menu.menu_key) {
        return
      }
      try {
        // 非初始化时才进行路由更新
        if (!init) {
          this.routerChange(menu)
        }

        this.$emit('menusChange', menu)
      } catch (error) {
        console.error('Failed to change menu:', error)
        this.$message.error('Failed to load menu data')
      }
    },
    routerChange(item) {
      const { path, query } = this.$route
      if (!item.menu_key || path.includes(item.menu_key)) {
        return
      }

      const name = this.menusData.menusNameMap[item.menu_key]
      if (name) {
        this.$router.push({
          path,
          name,
          query: {
            ...query
          }
        })
      }
    },
    async fetchMenuStep() {
      const isAttr = this.pageLevel === PAGE_LEVEL_DICT.att
      const key = isAttr ? 'common/getAttrSpuStepStatus2action' : 'common/getSpuStepStatus2action'
      const payload = isAttr
        ? {
            attraction_id: this.aid,
            language: this.language
          }
        : {
            spu_id: this.spuId || this.firstSpuId,
            language: this.language
          }

      await this[key](payload)
    },
    findMenuItem(menuKey) {
      const findItem = (list, key) => {
        for (let item of list) {
          if (item.menu_key === key) {
            return item
          }
          if (item.menu_tree_list && item.menu_tree_list.length > 0) {
            const found = findItem(item.menu_tree_list, key)
            if (found) {
              return found
            }
          }
        }
        return null
      }

      return findItem(this.menuList, menuKey)
    },
    initMainData() {
      let roles = klook.getAdminRole()
      this.$set(this.$root, 'roles', roles)
    },
    initLayout() {
      const layoutMain = this.$refs.layoutMain
      if (layoutMain) {
        const rect = layoutMain.getBoundingClientRect()
        this.scrollThreshold = rect.top + window.pageYOffset
        this.mainOffsetLeft = rect.left
        // 初始化时立即检查滚动状态
        this.onScroll()
      }
    },
    onScroll() {
      const scrollY = window.pageYOffset
      if (scrollY > this.scrollThreshold) {
        this.isFixed = true
        this.fixedTop = 65
      } else {
        this.isFixed = false
        this.fixedTop = 0
      }
    },
    registerSaveCallback(callback) {
      if (typeof callback === 'function') {
        this.saveCallback = callback
        return
      }
      if (typeof callback === 'object') {
        const { preSaveHook, postSaveHook, saveHook } = callback
        if (typeof preSaveHook === 'function') {
          this.preSaveHooks.push(preSaveHook)
        }
        if (typeof postSaveHook === 'function') {
          this.postSaveHooks.push(postSaveHook)
        }

        if (typeof saveHook === 'function') {
          this.saveCallback = saveHook
        }
      }
    },

    async executePreSaveHooks() {
      for (const hook of this.preSaveHooks) {
        try {
          const result = await hook()
          this.preSaveHooks = this.preSaveHooks.filter((h) => h !== hook)
          if (result === false) {
            console.warn('保存前钩子返回 false，取消保存')
            return false
          }
        } catch (error) {
          console.error('执行保存前钩子出错:', error)
          this.preSaveHooks = this.preSaveHooks.filter((h) => h !== hook)
          return false
        }
      }
      return true
    },

    async executePostSaveHooks(response) {
      for (const hook of this.postSaveHooks) {
        try {
          await hook(response)
        } catch (error) {
          console.error('保存后钩子执行失败:', error)
        } finally {
          this.postSaveHooks = []
        }
      }
      this.postSaveHooks = []
    },

    async updateMenuStatus() {
      const isAttr = this.pageLevel === PAGE_LEVEL_DICT.att
      const key = isAttr ? 'common/updateAttrMenuStatus2action' : 'common/updateMenuStatus2action'
      const payload = isAttr
        ? {
            attraction_id: this.aid,
            language: this.language,
            step: this.currentMenuKey
          }
        : {
            spu_id: +this.spuId,
            language: this.language,
            step: this.currentMenuKey
          }
      return await this[key](payload)
    },
    async saveThisSection(item) {
      if (this.globalSaveIsLoading) return
      let resp
      try {
        const canProceed = await this.executePreSaveHooks()
        if (!canProceed) {
          return
        }
        this[SET_LOADING](true)

        const pageType = this.currentItemData?.extra_info?.page_type
        // 特殊标准楼层有自己的保存逻辑
        const ownSaveCallback = this.policyKeys.includes(this.currentItemData.menu_key)
        if (!ownSaveCallback && pageType === PAGE_TYPE_DICT.standard) {
          resp = await this.saveStandardPage()
        } else if (typeof this.saveCallback === 'function') {
          resp = await this.saveCallback()
        } else {
          console.error('未设置自定义保存回调')
        }

        // 保存成功后更新菜单状态
        if (resp?.success || resp === true) {
          await this.updateMenuStatus()
          if (this.currentItemData.menu_key === TOURS_MENU_KEY_DICT.basic.key) {
            klook.bus.$emit('getSpuFloatingFields2bus') // 同步activity titile
          }
          this[SET_LOADING](false)
          // 跳转下一步
          if (!this.isLastItem && !resp?.noNext) {
            this.goForward()
          }
        }

        await this.executePostSaveHooks(resp)
      } catch (error) {
        console.error('保存时的错误:', error)
      } finally {
        this[SET_LOADING](false)
        if (item.key === 'save_and_next_step') {
          const extObj = {
            IsValid: Number(!!resp?.success) // 表示前端校验是否通过
          }
          this.$root.trackIHEvent(`.${item.key}`, extObj)
        }
      }
    },

    async saveStandardPage() {
      // 获取子组件的保存数据
      const childComponent = this.$refs.childComponent
      if (!childComponent || typeof childComponent.save !== 'function') {
        return
      }
      try {
        const data = await childComponent.save()
        if (!data) {
          scrollFormItemExplain()
          return
        }

        const { field_items, option_groups = [] } = fmtSaveDataIncludeDisplay(data)
        if (!field_items) {
          return
        }
        const saveData = {
          spu_id: this.pkgId,
          sub_category_id: this.subcategoryId,
          menu_key: this.currentMenuKey,
          page_level: this.pageLevel,
          page_from: this.pageFrom,
          language: this.language,
          activity_id: this.aid,
          field_items,
          option_groups
        }
        this.rootChangeLoading(true)
        const response = await saveProduct(saveData)
        this.rootChangeLoading(false)
        if (response.success) {
          this.$message.success(this.$t('act_save_success'))
        } else {
          this.$message.error(this.$t('act_save_fail'))
        }

        return response
      } catch (error) {
        console.error('标准页面保存时的错误:', error)
      }
    },
    getFlattenMenus(list = this.menuList) {
      const result = []
      const flatten = (items) => {
        items.forEach((item) => {
          if (!item.disabled && item.parent_id !== 0) {
            result.push(item)
          }
          if (item.menu_tree_list?.length) {
            flatten(item.menu_tree_list)
          }
        })
      }
      flatten(list)
      return result
    },
    getCurrentMenuIndex() {
      const flatMenus = this.getFlattenMenus()
      return flatMenus.findIndex((item) => item.menu_key === this.currentMenuKey)
    },

    // 找到上一个可用的菜单项
    findPreviousMenuItem() {
      const flatMenus = this.getFlattenMenus()
      const currentIndex = this.getCurrentMenuIndex()

      if (currentIndex > 0) {
        return flatMenus[currentIndex - 1]
      }
      return null
    },

    // 找到下一个可用的菜单项
    findNextMenuItem() {
      const flatMenus = this.getFlattenMenus()
      const currentIndex = this.getCurrentMenuIndex()

      if (currentIndex < flatMenus.length - 1) {
        return flatMenus[currentIndex + 1]
      }
      return null
    },
    // 上一步
    async goBack() {
      const previousItem = this.findPreviousMenuItem()
      if (previousItem) {
        await this.menuChange(previousItem)
      }
    },
    // 下一步
    async goForward() {
      const nextItem = this.findNextMenuItem()
      if (nextItem) {
        await this.menuChange(nextItem)
      }
    },
    refreshPage(getData = false) {
      this.refreshKey = false
      this.$nextTick(() => {
        this.refreshKey = true
        // this.initializeMenu()
      })
      // 获取数据
      if (getData) {
        this.getSectionDataByMenu(this.currentItemData)
      }
    },
    rootChangeLoading(data) {
      this[SET_GLOBAL_LOADING](data)
    }
  }
}
</script>

<style lang="scss" scoped>
.layout {
  display: flex;
  flex-direction: column;
  background-color: #f5f5f5;
  min-height: calc(100vh - 56px);

  .layout-header {
    position: relative;
    background-color: #fff;
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
  }

  .layout-main {
    position: relative;
    display: flex;
    flex: 1;

    // 占位元素样式
    .sidebar-placeholder {
      flex-shrink: 0;
      visibility: hidden;
    }

    .layout-sidebar {
      background-color: #fff;
      border: 1px solid rgb(232, 232, 232);
      border-left: none;
      border-top: none;
      padding-top: 16px;
    }

    .layout-right {
      flex: 1;
      display: flex;
      flex-direction: column;
      min-width: 0;

      .layout-content {
        margin: 20px;
        padding-bottom: 50px;
        height: 100%;
        background-color: #fff;
      }
    }

    .layout-footer {
      position: fixed;
      bottom: 0;
      z-index: 800;
      height: 50px;
      right: 0px;
      height: 58px;
      display: flex;
      border-top: 1px solid rgb(232, 232, 232);
      align-items: center;
      background-color: #fff;
      padding: 0 20px;
    }
  }
}
</style>

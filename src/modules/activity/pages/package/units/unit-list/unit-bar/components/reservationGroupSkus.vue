<template>
  <div class="pass-container">
    <a-spin :spinning="spinning" :delay="100">
      <!-- className: common-list reservation-list group-list 在 script 有参与逻辑判断，不能随便修改 -->
      <!-- Reservation skus -->
      <div class="box" :class="{ '--has-error': !!errorMessage.left }">
        <header class="header">
          <a-checkbox
            :indeterminate="reservationRes.indeterminate"
            :checked="reservationRes.checkAll"
            :disabled="!reservationRes.data.length || disabled"
            @change="(evt) => checkAllReservation(evt.target.checked)"
          >
            <span class="text--bold">
              {{ reservationRes.checkedList.length }}/{{ reservationRes.data.length }} {{ $t('78334') }}
            </span>
          </a-checkbox>

          <TriggerNameToView
            ref="reservationTriggerNameToView"
            class="text--bold"
            :list="reservationRes.checkedList"
            @scrollToViewIndex="(data) => handleScrollToViewIndex(data, 'reservation-list')"
          >
            <span slot="title">
              {{ $t('78330') }}
            </span>
          </TriggerNameToView>
        </header>

        <a-input-search
          v-model="reservationRes.searchValue"
          class="search"
          allow-clear
          :placeholder="$t('78333')"
          :disabled="!reservationRes.data.length || disabled"
          @change="
            (evt) =>
              localSearchSkus({
                query: evt.target.value,
                type: 'reservation'
              })
          "
        />

        <a-checkbox-group
          ref="left"
          :key="`${leftRefreshNum}_${reservationRes.data.length}`"
          v-model="reservationRes.checkedList"
          class="common-scrollbar content-left reservation-list common-list --data-type"
          :data-type="reservationRes.key"
          @change="checkReservation"
        >
          <a-empty v-if="!reservationRes.data.length" class="--is-ok" :description="false">
            <a-icon slot="image" type="check-circle" />
          </a-empty>

          <a-empty v-else-if="reservationRes.hiddenIdsList.length === reservationRes.data.length"></a-empty>

          <div
            v-for="(item, index) of reservationRes.data"
            :key="`${item.sku_id}-${index}`"
            :class="{
              '--display-none': reservationRes.hiddenIdsList.includes(item.sku_id)
            }"
            class="reservation-item common-item --data-type"
            :data-value="item.sku_id"
            :data-type="reservationRes.key"
          >
            <a-checkbox class="dd" :value="item.sku_id" @click="handleReservationMultiCheck">
              <span
                v-tooltip="{
                  visible: true,
                  delay: 500,
                  content: getTooltipContentByItem(item)
                }"
                class="item-label common-ellipsis-style"
              >
                {{ item.activity_id }}-{{ item.package_id }}-{{ item.unit_name }} ({{ item.activity_name }} -
                {{ item.package_name }})
              </span>
            </a-checkbox>

            <span v-if="!disabled" class="handle">
              <svg-icon icon-name="menu" />
            </span>
          </div>
        </a-checkbox-group>

        <div v-if="errorMessage.left" class="error-message" v-html="errorMessage.left"></div>
      </div>

      <div class="hint">
        {{ $t('78332').split(' ').join('\n') }}
        <br />
        >>
      </div>

      <!-- Group -->
      <div class="box" :class="{ '--has-error': !!errorMessage.right }">
        <header class="header">
          <a-checkbox
            :indeterminate="groupRes.indeterminate"
            :checked="groupRes.checkAll"
            :disabled="!allGroupData.length || disabled"
            @change="(evt) => checkAllGroup(evt.target.checked)"
          >
            <span class="text--bold">
              {{ groupRes.checkedList.length }}/{{ allGroupData.length }} {{ $t('78334') }}
            </span>
          </a-checkbox>

          <span class="text--bold">
            {{ groupsNumText }}
          </span>
        </header>

        <a-input-search
          v-model="groupRes.searchValue"
          class="search"
          allow-clear
          :placeholder="$t('78333')"
          :disabled="!allGroupData.length || disabled"
          @change="
            (evt) =>
              localSearchSkus({
                query: evt.target.value,
                type: 'groups'
              })
          "
        />

        <a-checkbox-group
          ref="right"
          :key="`${rightRefreshNum}_${groups.length}`"
          v-model="groupRes.checkedList"
          class="common-scrollbar group-list common-list"
          :disabled="disabled"
          @change="checkGroup"
        >
          <template v-for="group of groups">
            <div
              :key="`${group.data.length}-${group.title}`"
              :data-type="group.key"
              class="group-item-title --data-type"
              @click="handleFold(group.key)"
            >
              <a-icon
                class="item-triangle"
                :type="groupRes.unfoldKeys.includes(group.key) ? 'caret-down' : 'caret-right'"
              />
              {{ group.title }} ({{ group.data.length }})
            </div>

            <!-- <a-empty
              v-show="groupRes.unfoldKeys.includes(group.key)"
              v-if="!group.data.length"
              :key="group.key"
            /> -->

            <div
              v-for="item of group.data"
              v-show="groupRes.unfoldKeys.includes(group.key)"
              :key="`${group.key}-${item.sku_id}`"
              :class="{
                '--display-none': groupRes.hiddenIdsList.includes(item.sku_id)
              }"
              class="group-item-result-item common-item --data-type"
              :data-value="item.sku_id"
              :data-type="group.key"
            >
              <a-checkbox :value="item.sku_id" @click="handleGroupMultiCheck">
                <span
                  v-tooltip="{
                    visible: true,
                    delay: 500,
                    content: getTooltipContentByItem(item)
                  }"
                  class="item-label common-ellipsis-style"
                >
                  {{ item.activity_id }}-{{ item.package_id }}-{{ item.unit_name }} ({{
                    item.activity_name
                  }}
                  - {{ item.package_name }})
                </span>
              </a-checkbox>

              <span v-if="!disabled" class="handle">
                <svg-icon icon-name="menu" />
              </span>
            </div>
          </template>
        </a-checkbox-group>

        <div v-if="errorMessage.right" class="error-message" v-html="errorMessage.right"></div>
      </div>
    </a-spin>
  </div>
</template>

<script>
import TriggerNameToView from '@activity/pages/package/units/unit-list/unit-bar/components/triggerNameToView.vue'
import { Sortable, MultiDrag } from 'sortablejs'

const initSortableInstance_mixin = {
  data() {
    return {
      spinning: false
    }
  },
  computed: {
    // https://github.com/SortableJS/Sortable
    commonDragConf() {
      return {
        sort: true,
        multiDrag: true,
        invertSwap: true,
        selectedClass: 'selected',
        avoidImplicitDeselect: true,
        handle: '.handle',
        animation: 150,
        group: 'pass',
        setData: this.setData,
        onChange: this.onChange,
        onStart: this.onStart,
        onEnd: this.onEnd
      }
    }
  },
  mounted() {
    this.useMultiDragPlugin()

    this.$set(
      this.groupRes,
      'unfoldKeys',
      this.groups.reduce((acc, curr) => {
        if (curr.data?.length) {
          return [...acc, curr.key]
        }

        return acc
      }, [])
    )
  },
  methods: {
    // 移除因拖拽热区外的操作而导致多选勾选被清除的问题
    hackAvoidImplicitDeselect(deselectMultiDrag) {
      if (deselectMultiDrag) {
        document.removeEventListener('pointerup', deselectMultiDrag, false)
        document.removeEventListener('mouseup', deselectMultiDrag, false)
        document.removeEventListener('touchend', deselectMultiDrag, false)
      }
    },
    useMultiDragPlugin() {
      if (this.disabled) {
        return
      }

      try {
        this.multiDragInstance = new MultiDrag()
        Sortable.mount(this.multiDragInstance)
      } catch (e) {
        //
      } finally {
        this.initLeftSortableInstance()
        this.initRightSortableInstance()
      }
    },
    initLeftSortableInstance() {
      this.leftSortableInstance = Sortable.create(document.querySelector('.reservation-list'), {
        ...this.commonDragConf,
        selectedClass: 'selected',
        onSelect: (evt) => {
          const value = +evt.item.dataset.value

          if (!this.reservationRes.checkedList.includes(value)) {
            this.reservationRes.checkedList.push(value)
            this.checkReservation(this.reservationRes.checkedList)
          }
        },
        onDeselect: (evt) => {
          const value = +evt.item.dataset.value

          if (this.reservationRes.checkedList.includes(value)) {
            this.reservationRes.checkedList.splice(
              this.reservationRes.checkedList.findIndex((item) => item === value),
              1
            )
            this.checkReservation(this.reservationRes.checkedList)
          }
        }
      })

      this.hackAvoidImplicitDeselect(this.leftSortableInstance.multiDrag._deselectMultiDrag)
    },
    initRightSortableInstance() {
      const ele = document.querySelector('.group-list')

      this.rightSortableInstance = Sortable.create(ele, {
        ...this.commonDragConf,
        onSelect: (evt) => {
          const value = +evt.item.dataset.value

          if (!this.groupRes.checkedList.includes(value)) {
            this.groupRes.checkedList.push(value)
            this.checkGroup(this.groupRes.checkedList)
          }
        },
        onDeselect: (evt) => {
          const value = +evt.item.dataset.value

          if (this.groupRes.checkedList.includes(value)) {
            this.groupRes.checkedList.splice(
              this.groupRes.checkedList.findIndex((item) => item === value),
              1
            )
            this.checkGroup(this.groupRes.checkedList)
          }
        }
      })

      this.hackAvoidImplicitDeselect(this.rightSortableInstance.multiDrag._deselectMultiDrag)
    }
  }
}

const reservation_mixin = {
  data() {
    return {
      reservationRes: {
        key: 'reservation',
        checkAll: false,
        indeterminate: false,
        checkedList: [],
        data: [],
        searchValue: '',
        hiddenIdsList: []
      },

      leftRefreshNum: 0
    }
  },
  watch: {
    'reservationRes.data': {
      deep: true,
      handler() {
        this.refreshLeftSortableInstance()
        this.hackAvoidImplicitDeselect(this.leftSortableInstance?.multiDrag?._deselectMultiDrag)
      }
    },
    leftRefreshNum() {
      this.refreshLeftSortableInstance()
    }
  },
  methods: {
    async refreshLeftSortableInstance() {
      await this.$nextTick()

      this.initLeftSortableInstance()
    },
    handleScrollToViewIndex({ value }, selector) {
      const data = selector === 'reservation-list' ? this.reservationRes.data : this.allGroupData
      const index = data.findIndex((item) => item.sku_id === value)
      const container = document.querySelector(`.${selector}`)
      const items = container.querySelectorAll('.common-item')

      container.scrollTop =
        items[index].getBoundingClientRect().top - container.getBoundingClientRect().top + container.scrollTop
    },
    // query: string, can split ,|，
    // type: reservation | groups
    localSearchSkus: _.debounce(function ({ query, type, dataKey = 'sku_id' } = {}) {
      query = query.trim()
      let hiddenIdsList = []

      if (query.length) {
        let res = {}
        const queryStr = query.split(/,|，/g).filter((str) => str.trim().length)
        const data = type === 'reservation' ? this.reservationRes.data : this.allGroupData

        // 触发搜索会使得 items 显示或隐藏，所以需要重置 index
        if (type === 'reservation') {
          this.$refs.reservationTriggerNameToView?.refreshIndex?.()
        }

        // 计算权重
        data.forEach((item) => {
          const key = item[dataKey]
          res[key] = 0
          ;['activity_id', 'activity_name', 'package_id', 'package_name', 'unit_name', 'sku_id'].forEach(
            (field) => {
              queryStr.forEach((str) => {
                str = str.toLocaleLowerCase()
                if (~String(item[field]).toLocaleLowerCase().indexOf(str)) {
                  res[key]++
                }
              })
            }
          )
        })

        // 权重筛选和排序
        // const weightRes = _.orderBy(
        //   Object.entries(res)
        //     .map((item) => ({
        //       key: item[0],
        //       amount: item[1]
        //     }))
        //     .filter((item) => item.amount),
        //   ['amount'],
        //   ['desc']
        // )
        hiddenIdsList = Object.entries(res)
          .map((item) => ({
            key: item[0],
            amount: item[1]
          }))
          .filter((item) => !item.amount)
          .map((item) => +item.key)
      }

      const data = this[type === 'reservation' ? 'reservationRes' : 'groupRes']
      // first reset then set
      // this.$set(data, 'hiddenIdsList', [])
      this.$nextTick(() => {
        this.$set(data, 'hiddenIdsList', hiddenIdsList)
      })

      if ('groups' === type) {
        const unfoldKeys = this.groups.reduce((acc, curr) => {
          if (curr.data.some((item) => !hiddenIdsList.includes(item[dataKey]))) {
            acc.push(curr.key)
          }

          return acc
        }, [])

        this.$set(this.groupRes, 'unfoldKeys', unfoldKeys)
      }
    }, 200),
    handleReservationMultiCheck(evt) {
      const node = this.getParentNode(evt.target)

      if (node && this.multiDragInstance) {
        const key = this.getMultiSelectFuncKey(this.reservationRes.checkedList.includes(+node.dataset.value))

        this.multiDragInstance.utils[key](node)
      }
    },
    checkAllReservation(checked) {
      const result = this.getCheckAllData(checked, {
        data: this.reservationRes.data,
        selector: '.reservation-list .common-item'
      })

      Object.assign(this.reservationRes, result)
    },
    checkReservation: _.debounce(function checkReservation(checkedList) {
      this.reservationRes.indeterminate =
        !!checkedList.length && checkedList.length < this.reservationRes.data.length
      this.reservationRes.checkAll = checkedList.length === this.reservationRes.data.length
    }, 30)
  }
}

const group_mixin = {
  data() {
    return {
      rightRefreshNum: 0,

      groupRes: {
        unfoldKeys: [],
        checkedList: [],
        checkAll: false,
        indeterminate: false,
        hiddenIdsList: [],
        searchValue: ''
      },

      groupSortableInstance: {}
    }
  },
  watch: {
    allGroupData: {
      deep: true,
      handler(v) {
        this.rightRefreshNum++

        if (!v.length || !this.groupRes.checkedList.length) {
          this.checkAllGroup(false)
        } else if (this.groupRes.checkedList.length === v.length) {
          this.checkAllGroup(true)
        }

        this.hackAvoidImplicitDeselect(this.rightSortableInstance?.multiDrag?._deselectMultiDrag)
      }
    },
    groups: {
      deep: true,
      handler() {
        this.rightRefreshNum++
      }
    },
    rightRefreshNum() {
      this.refreshRightSortableInstance()
    }
  },
  computed: {
    groupsNumText() {
      return klook.parseStr1(this.$t('78331'), {
        num: this.groups.length
      })
    },
    allGroupData() {
      return this.groups.reduce((acc, curr) => [...acc, ...curr.data], [])
    }
  },
  methods: {
    async refreshRightSortableInstance() {
      await this.$nextTick()

      this.initRightSortableInstance()
    },
    // 遍历 groups 然后塞进对应的 data 中
    updateGroupDataWhenDragEnd(items) {
      const multiDragValueList = items.map((item) => +item.multiDragElement.dataset.value)
      this.groups.forEach((group, index) => {
        const groupValue = group.data.map((item) => item.sku_id)
        if (_.intersection(groupValue, multiDragValueList).length) {
          const newGroupData = group.data.filter((item) => !multiDragValueList.includes(item.sku_id))
          this.$set(this.groups[index], 'data', newGroupData)
        }
      })
    },
    handleGroupMultiCheck(evt) {
      const node = this.getParentNode(evt.target)

      if (node) {
        const key = this.getMultiSelectFuncKey(this.groupRes.checkedList.includes(+node.dataset.value))

        this.multiDragInstance.utils[key](node)
      }
    },
    checkAllGroup(checked) {
      const result = this.getCheckAllData(checked, {
        data: this.allGroupData,
        selector: '.group-list .common-item'
      })

      Object.assign(this.groupRes, result)
    },
    checkGroup: _.debounce(function checkGroup(checkedList) {
      this.groupRes.indeterminate = !!checkedList.length && checkedList.length < this.allGroupData.length
      this.groupRes.checkAll = checkedList.length === this.allGroupData.length
    }, 30),
    handleFold(key) {
      if (this.groupRes.unfoldKeys.includes(key)) {
        const unfoldKeys = this.groupRes.unfoldKeys.filter((item) => item !== key)
        this.$set(this.groupRes, 'unfoldKeys', unfoldKeys)
      } else {
        this.groupRes.unfoldKeys.push(key)
      }
    }
  }
}

export default {
  name: 'ReservationGroupSkus',
  components: {
    TriggerNameToView
  },
  mixins: [initSortableInstance_mixin, reservation_mixin, group_mixin],
  props: {
    data: {
      type: Array,
      required: true
    },
    removeGroupSkuIds: {
      type: Array,
      default: () => []
    },
    insertGroupSkuData: {
      type: Array,
      default: () => []
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    this.CLASSNAME_DRAG_LANDING = '--drag-landing'

    return {
      errorMessage: {
        left: '',
        right: ''
      }
    }
  },
  computed: {
    groups: {
      get() {
        return this.data
      },
      set() {}
    },
    allSkuListData() {
      return [...this.reservationRes.data, ...this.allGroupData]
    }
  },
  watch: {
    removeGroupSkuIds: {
      deep: true,
      immediate: true,
      handler(v) {
        this.$nextTick(() => {
          if (v.length) {
            this.groups.forEach((group, index) => {
              const data = _.cloneDeep(group.data).filter((item) => !v.includes(item.sku_id))

              if (!_.isEqualWith(data, group.data)) {
                this.$set(this.groups[index], 'data', data)
              }
            })

            const reservationData = _.cloneDeep(this.reservationRes.data).filter(
              (item) => !v.includes(item.sku_id)
            )

            if (!_.isEqualWith(reservationData, this.reservationRes.data)) {
              this.$set(this.reservationRes, 'data', reservationData)
            }

            this.$emit('update:removeGroupSkuIds', [])
          }
        })
      }
    },
    insertGroupSkuData: {
      deep: true,
      immediate: true,
      handler(v) {
        setTimeout(() => {
          if (v.length) {
            this.reservationRes.data.push(...v)
            this.$emit('update:insertGroupSkuData', [])
          }
        })
      }
    }
  },
  methods: {
    validator() {
      this.$set(this, 'errorMessage', this.$options.data().errorMessage)

      if (this.reservationRes.data.length) {
        this.errorMessage.left = this.$t('78606')

        return {
          valid: false,
          message: this.errorMessage.left
        }
      }

      if (this.groups.some((group) => !group.data.length)) {
        this.errorMessage.right = this.$t('78607')

        return {
          valid: false,
          message: this.errorMessage.right
        }
      }

      return {
        valid: true
      }
    },
    getTooltipContentByItem(item) {
      const { activity_id, activity_name, package_id, package_name, unit_name, sku_id } = item

      return [
        `${activity_id} - ${activity_name}`,
        `${package_id} - ${package_name}`,
        `${sku_id} - ${unit_name}`
      ].join('<br />')
    },
    rewriteDragTriggerAmountHTML(triggerEle, amount) {
      const label = triggerEle.querySelector('.item-label')

      this.$nextTick(() => {
        label.innerText = klook.parseStr1(this.$t('78347'), {
          num: amount || 1
        })
      })
    },
    recoverDragTriggerHTML(triggerEle) {
      if (this.cacheDragTriggerItemNode?.style?.display === 'none') {
        this.cacheDragTriggerItemNode.style.display = 'flex'
        this.cacheDragTriggerItemNode = null
      }

      triggerEle.querySelector('.item-label').innerText = this.cacheDragTriggerLabelEleText
      this.cacheDragTriggerLabelEleText = null
    },
    multiSelectNode(selector, checkedList) {
      ;[...document.querySelector(`.${selector}`).querySelectorAll('.common-item')].forEach((node) => {
        if (checkedList.includes(+node.dataset.value)) {
          this.multiDragInstance?.utils?.select?.(node)
        }
      })
    },
    // 在 clone 前，对拖拽对象(触发 DOM) 修改 text
    setData(dataTransfer, dragEl) {
      const dragTriggerLabelEle = dragEl.querySelector('.item-label')

      this.currentFromLandEle = this.getParentNode(dragEl, '--data-type')

      if (dragTriggerLabelEle) {
        const itemNodeEle = this.getParentNode(dragTriggerLabelEle)
        const { type, value } = itemNodeEle.dataset

        if (type === 'reservation') {
          if (!this.reservationRes.checkedList.includes(+value)) {
            this.reservationRes.checkedList.push(+value)
            this.multiSelectNode('reservation-list', this.reservationRes.checkedList)
          }
        } else {
          if (!this.groupRes.checkedList.includes(+value)) {
            this.groupRes.checkedList.push(+value)
            this.multiSelectNode('group-list', this.groupRes.checkedList)
          }
        }

        const list = this.getParentNode(dragEl, 'common-list')
        const amount = list.classList.contains('group-list')
          ? this.groupRes.checkedList.length
          : this.reservationRes.checkedList.length

        this.cacheDragTriggerLabelEleText = dragTriggerLabelEle.innerText
        // mouse drag target ele
        dragTriggerLabelEle.innerText = klook.parseStr1(this.$t('78347'), {
          num: amount || 1
        })
        setTimeout(() => {
          const node = this.getParentNode(dragTriggerLabelEle, 'common-item')

          if (node) {
            node.style.display = 'none'
            this.cacheDragTriggerItemNode = node
          }
        }, 1000 / 60)
      }
    },
    removeDragendClass() {
      document
        .querySelector(`.${this.CLASSNAME_DRAG_LANDING}`)
        ?.classList?.remove?.(this.CLASSNAME_DRAG_LANDING)
    },
    onChange(evt) {
      const { to, originalEvent } = evt

      const lands = ['group-list', 'reservation-list']
      for (let land of lands) {
        if (to.classList.contains(land)) {
          this.removeDragendClass()

          let { target } = originalEvent
          let groupItemTitleDom = this.getParentNode(target, 'group-item-title')

          // 解决拖拽在空白处，无法定位 group title 的问题
          // 默认取 last group item
          if (!to.classList.contains('reservation-list') && !groupItemTitleDom) {
            let groupListDom = this.getParentNode(target, 'group-list')
            let reservationListDom = this.getParentNode(target, 'reservation-list')
            if (groupListDom || !reservationListDom) {
              land = 'group-list'
              target = Array.from(target.querySelectorAll('.group-item-title')).splice(-1)[0]
            }
          }

          if (land === 'reservation-list') {
            const node = this.getParentNode(target, 'common-list')

            node?.classList?.add?.(this.CLASSNAME_DRAG_LANDING)
          } else {
            target.classList.add(this.CLASSNAME_DRAG_LANDING)
          }

          this.currentToLandEle = target
          this.manualSettingToLand = target.classList.contains(land)
        }
      }
    },
    onStart(evt) {
      const {
        items,
        originalEvent: { target: triggerEle }
      } = evt

      this.rewriteDragTriggerAmountHTML(triggerEle, items.length)

      this.preUnfoldKeys = this.groupRes.unfoldKeys
      this.$set(this.groupRes, 'unfoldKeys', [])
    },
    onEnd(evt) {
      let { to, items, from, item: triggerEle } = evt

      // 同个 sortable instance 拖拽操作的时候，会丢失 to/from 触发信息
      if (to.classList.contains('group-list') || this.manualSettingToLand) {
        this.manualSettingToLand = false
        to = this.currentToLandEle
      }

      if (from.classList.contains('group-list')) {
        from = this.currentFromLandEle
      }

      const reservationKey = this.reservationRes.key
      let toKey = this.getTargetTypeByDataset(to)
      const fromKey = this.getTargetTypeByDataset(from)

      const checkedValue = items.map((node) => +node.dataset.value)
      const newInsertData = this.allSkuListData.filter((item) => checkedValue.includes(item.sku_id))
      let unfoldKeys = this.preUnfoldKeys || []

      // reset element status
      this.removeDragendClass()
      this.recoverDragTriggerHTML(triggerEle)

      // deselect all items
      items.forEach((item) => {
        item.style.display = 'none'
        this.multiDragInstance.utils.deselect(item)
      })

      // 1. reservation --> reservation
      if (toKey === fromKey && toKey === reservationKey) {
        this.$set(this.groupRes, 'unfoldKeys', unfoldKeys)

        this.$set(this, 'reservationRes', {
          ...this.reservationRes,
          data: [...new Set([...this.reservationRes.data, ...newInsertData])],
          checkedList: checkedValue
        })

        this.checkReservation(checkedValue)

        // delete clone items
        items.forEach((item) => item.remove())

        this.leftRefreshNum++

        return
      }

      // 2. group --> reservation
      if (toKey === reservationKey) {
        // drag to reservation
        // this.reservationRes.checkedList.push(checkedValue)
        // this.checkReservation(this.reservationRes.checkedList)
        this.reservationRes.data.push(...newInsertData)

        // drag from group
        const groupCheckedList = this.groupRes.checkedList.filter((res) => !checkedValue.includes(res))
        this.$set(this.groupRes, 'checkedList', groupCheckedList)

        this.updateGroupDataWhenDragEnd(evt.newIndicies)
        this.$set(this.groupRes, 'unfoldKeys', unfoldKeys)

        this.leftRefreshNum++

        return
      }

      if (fromKey === reservationKey) {
        // 3. reservation --> group
        // drag from reservation
        this.$set(this, 'reservationRes', {
          ...this.reservationRes,
          data: this.reservationRes.data.filter((item) => !checkedValue.includes(item.sku_id)),
          checkedList: [],
          indeterminate: false,
          checkAll: false
        })

        this.leftRefreshNum++
      } else {
        // 4. group --> group
        // drag from group
        this.updateGroupDataWhenDragEnd(evt.newIndicies)
      }

      // to group
      const groupIndex = this.groups.findIndex((group) => group.key === toKey)
      const newGroupData = [...new Set([...this.groups[groupIndex].data, ...newInsertData])]
      this.$set(this.groups[groupIndex], 'data', newGroupData)

      Object.assign(this.groupRes, {
        checkedList: [],
        unfoldKeys: _.intersection(
          this.groups.filter((group) => group.data.length).map((group) => group.key),
          [...unfoldKeys, toKey]
        ),
        checkAll: false,
        indeterminate: false
      })

      // delete clone items
      items.forEach((item) => item.remove())

      // refresh search view
      this.rightRefreshNum++

      // scroll to toKey ele
      this.scrollGroupToView(toKey)

      this.$emit('onEnd')
    },
    async scrollGroupToView(toSelector) {
      await this.$nextTick()

      const container = document.querySelector('.group-list')
      const toEle = document.querySelector(`.group-item-title[data-type="${toSelector}"]`)

      if (toEle) {
        container.scrollTop =
          toEle.getBoundingClientRect().top - container.getBoundingClientRect().top + container.scrollTop
      }
    },
    getCheckAllData(checked, { data, selector }) {
      let checkedList = []
      const funcKey = this.getMultiSelectFuncKey(!checked)

      if (checked) {
        checkedList = data.map((item) => item.sku_id)
      }

      const reservationNodeList = [...document.querySelectorAll(selector)]
      reservationNodeList.forEach((node) => {
        this.multiDragInstance && this.multiDragInstance.utils[funcKey](node)
      })

      return {
        checkedList,
        indeterminate: false,
        checkAll: checked
      }
    },
    getTargetTypeByDataset(node) {
      node = this.getParentNode(node, '--data-type')
      if (!node) {
        return ''
      }

      const dataset = node.dataset

      if (dataset.type === this.reservationRes.key) {
        return this.reservationRes.key
      }

      for (let group of this.groups) {
        if (dataset.type === group.key) {
          return group.key
        }
      }

      return ''
    },
    getMultiSelectFuncKey(checked) {
      return checked ? 'deselect' : 'select'
    },
    getParentNode(node, className = 'common-item') {
      let MAX = 8

      while (MAX && node) {
        if (node.classList.contains(className)) {
          return node
        }

        MAX--
        node = node.parentNode
      }

      return null
    }
  }
}
</script>

<style lang="scss" scoped>
.pass-container {
  width: 875px;
  margin-bottom: 6px;

  ::v-deep .ant-spin-container {
    display: flex;
    align-items: center;
  }

  .text--bold {
    font-weight: 400px;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.85);
  }

  .hint {
    margin: 0 10px;
    word-wrap: break-word;
    white-space: pre-line;
    text-align: center;
  }

  .common-item {
    display: flex;
    justify-content: space-between;
    padding: 6px 12px;
    transition: background-color 0.4s;

    ::v-deep .ant-checkbox {
      position: relative;
      top: -2px;
    }

    .item-label {
      color: rgba(0, 0, 0, 0.85);
      cursor: pointer;
    }

    &:hover .item-label {
      color: #666;
    }

    // &.selected {
    //   font-weight: bolder;
    //   background-color: #e6e6e6;
    // }

    ::v-deep .ant-checkbox-wrapper {
      max-width: 100%;
      display: flex;
      flex: 1;
      align-items: center;
    }
  }

  .handle {
    cursor: grab;
  }

  .common-list {
    height: calc(100% - 22px - 32px - 40px);
    width: calc(100% + 24px);
    margin-left: -12px;
    overflow-x: hidden;
    overflow-y: auto;

    // &::-webkit-scrollbar-thumb {
    //   display: none;
    // }

    // &:hover::-webkit-scrollbar-thumb {
    //   display: block;
    // }
  }

  .box {
    width: 368px;
    height: 360px;
    padding: 12px;
    box-sizing: border-box;
    background-color: #fafafa;
    border: 1px solid transparent;
    border-radius: 2px;

    &.--has-error {
      border-color: #f5222d;
    }

    .header {
      display: flex;
      justify-content: space-between;
      line-height: 22px;
    }

    .search {
      margin: 20px 0;
    }

    .group-list {
      padding: 0 12px;

      ::v-deep .ant-checkbox-group {
        width: 100%;
      }
    }

    .group-item {
      padding: 0 12px;
      margin-bottom: 6px;

      &:last-child {
        margin-bottom: 0;
      }

      &-title {
        position: sticky;
        top: 0;
        padding-bottom: 6px;
        background-color: #fafafa;
        z-index: 999;
        cursor: pointer;

        &:hover {
          opacity: 0.8;
        }
      }

      &-result-list {
        padding: 12px;
      }
    }
  }

  .error-message {
    position: relative;
    left: -12px;
    width: 368px;
    padding-top: 12px;
    color: #ff4d4f;
  }

  .--drag-landing {
    background-color: #eee !important;
    color: #ff5b00;
  }

  .--display-none {
    display: none;
  }

  .common-ellipsis-style {
    max-width: 292px;
  }

  .group-list .common-ellipsis-style {
    max-width: 268px;
  }

  .--is-ok {
    position: relative;
    top: 60px;
    font-size: 52px;
    color: rgba(0, 179, 113, 0.6);
  }

  ::v-deep .ant-input {
    border: 1px solid #d9d9d9 !important;
  }
}
</style>
